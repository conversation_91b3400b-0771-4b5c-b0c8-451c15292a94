{"version": 3, "file": "app.js", "sources": ["App.vue", "main.js"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport { onLaunch, onShow, onHide } from '@dcloudio/uni-app';\r\nimport { useUserStore } from './src/stores/modules/user';\r\nimport { useAppStore } from './src/stores/modules/app';\r\n\r\nconst userStore = useUserStore();\r\nconst appStore = useAppStore();\r\n\r\nonLaunch(() => {\r\n  uni.__f__('log','at App.vue:10','App Launch');\r\n\r\n  // 初始化用户信息\r\n  userStore.initProfile();\r\n\r\n  // 初始化系统信息\r\n  appStore.initSystemInfo();\r\n\r\n  // 检查小程序更新\r\n  checkForUpdate();\r\n});\r\n\r\nonShow(() => {\r\n  uni.__f__('log','at App.vue:23','App Show');\r\n});\r\n\r\nonHide(() => {\r\n  uni.__f__('log','at App.vue:27','App Hide');\r\n});\r\n\r\n/**\r\n * 检查小程序更新\r\n */\r\nfunction checkForUpdate() {\r\n\r\n  const updateManager = uni.getUpdateManager();\r\n\r\n  updateManager.onCheckForUpdate((res) => {\r\n    uni.__f__('log','at App.vue:38','检查更新结果:', res.hasUpdate);\r\n  });\r\n\r\n  updateManager.onUpdateReady(() => {\r\n    uni.showModal({\r\n      title: '更新提示',\r\n      content: '新版本已经准备好，是否重启应用？',\r\n      success: (res) => {\r\n        if (res.confirm) {\r\n          updateManager.applyUpdate();\r\n        }\r\n      },\r\n    });\r\n  });\r\n\r\n  updateManager.onUpdateFailed(() => {\r\n    uni.showToast({\r\n      title: '更新失败，请稍后重试',\r\n      icon: 'none',\r\n    });\r\n  });\r\n\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n@import '@/src/styles/global.scss';\r\n\r\n/* 每个页面公共css */\r\n</style>\r\n", "import App from './App'\n\n// #ifndef VUE3\nimport Vue from 'vue'\nimport './uni.promisify.adaptor'\nVue.config.productionTip = false\nApp.mpType = 'app'\nconst app = new Vue({\n  ...App\n})\napp.$mount()\n// #endif\n\n// #ifdef VUE3\nimport { createSSRApp } from 'vue'\nimport pinia from './src/stores'\n\nexport function createApp() {\n  const app = createSSRApp(App)\n  app.use(pinia)\n  return {\n    app\n  }\n}\n// #endif"], "names": ["useUserStore", "useAppStore", "onLaunch", "uni", "onShow", "onHide", "createSSRApp", "App", "pinia"], "mappings": ";;;;;;;;;;;;;;;;AAKA,UAAM,YAAYA,wBAAAA;AAClB,UAAM,WAAWC,uBAAAA;AAEjBC,kBAAAA,SAAS,MAAM;AACTC,oBAAAA,MAAA,MAAM,OAAM,iBAAgB,YAAY;AAG5C,gBAAU,YAAY;AAGtB,eAAS,eAAe;AAGT;IAAA,CAChB;AAEDC,kBAAAA,OAAO,MAAM;AACPD,oBAAAA,MAAA,MAAM,OAAM,iBAAgB,UAAU;AAAA,IAAA,CAC3C;AAEDE,kBAAAA,OAAO,MAAM;AACPF,oBAAAA,MAAA,MAAM,OAAM,iBAAgB,UAAU;AAAA,IAAA,CAC3C;AAKD,aAAS,iBAAiB;AAElB,YAAA,gBAAgBA,oBAAI;AAEZ,oBAAA,iBAAiB,CAAC,QAAQ;AACtCA,sBAAA,MAAI,MAAM,OAAM,iBAAgB,WAAW,IAAI,SAAS;AAAA,MAAA,CACzD;AAED,oBAAc,cAAc,MAAM;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA,UACT,SAAS,CAAC,QAAQ;AAChB,gBAAI,IAAI,SAAS;AACf,4BAAc,YAAY;AAAA,YAC5B;AAAA,UACF;AAAA,QAAA,CACD;AAAA,MAAA,CACF;AAED,oBAAc,eAAe,MAAM;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AAAA,MAAA,CACF;AAAA,IAEH;;;;;AC1CO,SAAS,YAAY;AAC1B,QAAM,MAAMG,cAAY,aAACC,SAAG;AAC5B,MAAI,IAAIC,sBAAK;AACb,SAAO;AAAA,IACL;AAAA,EACD;AACH;;;"}