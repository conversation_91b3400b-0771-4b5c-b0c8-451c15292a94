<template>
  <view class="login-container">
    <view class="login-header">
      <image class="logo" src="/static/logo.png" mode="aspectFit" />
      <text class="app-name">疾控医护考试系统</text>
      <text class="app-desc">专业、权威、便捷的任职资格考试平台</text>
    </view>
    
    <view class="login-form">
      <view class="agreement-section">
        <checkbox-group @change="onAgreementChange">
          <label class="agreement-item">
            <checkbox :checked="agreedToTerms" />
            <text class="agreement-text">
              我已阅读并同意
              <text class="link" @tap="showUserAgreement">《用户服务协议》</text>
              和
              <text class="link" @tap="showPrivacyPolicy">《隐私政策》</text>
            </text>
          </label>
        </checkbox-group>
      </view>
      
      <button 
        class="login-btn" 
        :disabled="!agreedToTerms"
        @tap="handleWxLogin"
        open-type="getUserInfo"
        @getuserinfo="onGetUserInfo"
      >
        微信授权登录
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useUserStore } from '../../src/stores/modules/user';
import { wxLogin } from '../../src/api/modules/user';

const userStore = useUserStore();

// 响应式数据
const agreedToTerms = ref(false);

/**
 * 协议勾选状态变化
 */
function onAgreementChange(event: any) {
  agreedToTerms.value = event.detail.value.length > 0;
}

/**
 * 显示用户协议
 */
function showUserAgreement() {
  uni.showModal({
    title: '用户服务协议',
    content: '这里是用户服务协议的内容...',
    showCancel: false,
  });
}

/**
 * 显示隐私政策
 */
function showPrivacyPolicy() {
  uni.showModal({
    title: '隐私政策',
    content: '这里是隐私政策的内容...',
    showCancel: false,
  });
}

/**
 * 微信登录
 */
function handleWxLogin() {
  if (!agreedToTerms.value) {
    uni.showToast({
      title: '请先同意用户协议',
      icon: 'none',
    });
    return;
  }
  
  uni.login({
    provider: 'weixin',
    success: async (loginRes) => {
      try {
        const userInfo = await wxLogin({ code: loginRes.code });
        userStore.setProfile(userInfo);
        
        // 根据用户状态跳转
        if (userInfo.status === 'approved') {
          uni.switchTab({ url: '/pages/info/info' });
        } else {
          uni.navigateTo({ url: '/pages/register/register' });
        }
      } catch (error) {
        console.error('登录失败:', error);
      }
    },
    fail: (error) => {
      uni.showToast({
        title: '登录失败，请重试',
        icon: 'none',
      });
      console.error('微信登录失败:', error);
    },
  });
}

/**
 * 获取用户信息回调
 */
function onGetUserInfo(event: any) {
  console.log('用户信息:', event.detail);
}
</script>

<style lang="scss" scoped>
@import '../../src/styles/variables.scss';

.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, $primary-color 0%, $primary-light 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xl;
}

.login-header {
  text-align: center;
  margin-bottom: $spacing-xl * 2;
  
  .logo {
    width: 120rpx;
    height: 120rpx;
    margin-bottom: $spacing-lg;
  }
  
  .app-name {
    display: block;
    font-size: $font-size-xxl;
    font-weight: $font-weight-bold;
    color: white;
    margin-bottom: $spacing-sm;
  }
  
  .app-desc {
    display: block;
    font-size: $font-size-md;
    color: rgba(255, 255, 255, 0.8);
  }
}

.login-form {
  width: 100%;
  max-width: 600rpx;
}

.agreement-section {
  margin-bottom: $spacing-xl;
  
  .agreement-item {
    display: flex;
    align-items: flex-start;
    
    .agreement-text {
      flex: 1;
      font-size: $font-size-sm;
      color: rgba(255, 255, 255, 0.9);
      line-height: $line-height-loose;
      margin-left: $spacing-sm;
      
      .link {
        color: $accent-color;
        text-decoration: underline;
      }
    }
  }
}

.login-btn {
  width: 100%;
  height: 88rpx;
  background-color: white;
  color: $primary-color;
  font-size: $font-size-lg;
  font-weight: $font-weight-medium;
  border-radius: $border-radius-large;
  border: none;
  
  &:disabled {
    background-color: rgba(255, 255, 255, 0.5);
    color: rgba(25, 118, 210, 0.5);
  }
  
  &:not(:disabled):active {
    background-color: rgba(255, 255, 255, 0.9);
  }
}
</style>
