/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 全局样式变量
 * 疾控机构专业色彩方案：蓝绿色系
 */
/**
 * 全局样式变量
 * 疾控机构专业色彩方案：蓝绿色系
 */
.study-container.data-v-3f273c1e {
  min-height: 100vh;
  background-color: #fafafa;
  padding: 24rpx;
}
.study-header.data-v-3f273c1e {
  text-align: center;
  margin-bottom: 48rpx;
}
.study-header .page-title.data-v-3f273c1e {
  display: block;
  font-size: 40rpx;
  font-weight: 600;
  color: #212121;
  margin-bottom: 8rpx;
}
.study-header .page-desc.data-v-3f273c1e {
  font-size: 28rpx;
  color: #757575;
}
.study-modules.data-v-3f273c1e {
  margin-bottom: 48rpx;
}
.study-modules .module-card.data-v-3f273c1e {
  background-color: #ffffff;
  border-radius: 8rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.study-modules .module-card .module-icon.data-v-3f273c1e {
  font-size: 60rpx;
  margin-right: 24rpx;
}
.study-modules .module-card .module-info.data-v-3f273c1e {
  flex: 1;
}
.study-modules .module-card .module-info .module-title.data-v-3f273c1e {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #212121;
  margin-bottom: 8rpx;
}
.study-modules .module-card .module-info .module-desc.data-v-3f273c1e {
  display: block;
  font-size: 24rpx;
  color: #757575;
  margin-bottom: 8rpx;
}
.study-modules .module-card .module-info .module-status.data-v-3f273c1e {
  font-size: 20rpx;
  color: #1976d2;
}
.study-modules .module-card .module-arrow.data-v-3f273c1e {
  font-size: 32rpx;
  color: #bdbdbd;
}
.stats-section .stats-title.data-v-3f273c1e {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #212121;
  margin-bottom: 24rpx;
}
.stats-section .stats-cards.data-v-3f273c1e {
  display: flex;
  gap: 24rpx;
}
.stats-section .stats-cards .stats-card.data-v-3f273c1e {
  flex: 1;
  background-color: #ffffff;
  border-radius: 8rpx;
  padding: 32rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.stats-section .stats-cards .stats-card .stats-number.data-v-3f273c1e {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #1976d2;
  margin-bottom: 8rpx;
}
.stats-section .stats-cards .stats-card .stats-label.data-v-3f273c1e {
  font-size: 24rpx;
  color: #757575;
}