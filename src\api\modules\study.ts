/**
 * 学习中心相关API接口
 */
import http from '../../utils/request';
import type { QuestionCategory, Question, PracticeResult } from '../../types/api';

/**
 * 获取题库分类列表
 */
export function getQuestionCategories() {
  return http.get<QuestionCategory[]>('/study/categories');
}

/**
 * 获取练习题目
 */
export function getPracticeQuestions(categoryId: string, count: number = 10) {
  return http.get<Question[]>('/study/questions', { categoryId, count });
}

/**
 * 提交练习结果
 */
export function submitPracticeResult(params: PracticeResult) {
  return http.post<boolean>('/study/submit-result', params);
}

/**
 * 获取用户练习统计
 */
export function getPracticeStats() {
  return http.get<any>('/study/stats');
}
