/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 全局样式变量
 * 疾控机构专业色彩方案：蓝绿色系
 */
/**
 * 全局样式
 */
/**
 * 全局样式变量
 * 疾控机构专业色彩方案：蓝绿色系
 */
view, text, button, input, textarea, image, scroll-view {
  box-sizing: border-box;
}
page {
  background-color: #fafafa;
  color: #212121;
  font-size: 28rpx;
  line-height: 1.4;
}
.container {
  padding: 24rpx;
}
.flex {
  display: flex;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.flex-1 {
  flex: 1;
}
.text-primary {
  color: #212121;
}
.text-secondary {
  color: #757575;
}
.text-disabled {
  color: #bdbdbd;
}
.text-success {
  color: #4caf50;
}
.text-warning {
  color: #ff9800;
}
.text-error {
  color: #f44336;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.font-bold {
  font-weight: 600;
}
.font-medium {
  font-weight: 500;
}
.m-xs {
  margin: 8rpx;
}
.m-sm {
  margin: 16rpx;
}
.m-md {
  margin: 24rpx;
}
.m-lg {
  margin: 32rpx;
}
.m-xl {
  margin: 48rpx;
}
.mt-xs {
  margin-top: 8rpx;
}
.mt-sm {
  margin-top: 16rpx;
}
.mt-md {
  margin-top: 24rpx;
}
.mt-lg {
  margin-top: 32rpx;
}
.mt-xl {
  margin-top: 48rpx;
}
.mb-xs {
  margin-bottom: 8rpx;
}
.mb-sm {
  margin-bottom: 16rpx;
}
.mb-md {
  margin-bottom: 24rpx;
}
.mb-lg {
  margin-bottom: 32rpx;
}
.mb-xl {
  margin-bottom: 48rpx;
}
.ml-xs {
  margin-left: 8rpx;
}
.ml-sm {
  margin-left: 16rpx;
}
.ml-md {
  margin-left: 24rpx;
}
.ml-lg {
  margin-left: 32rpx;
}
.ml-xl {
  margin-left: 48rpx;
}
.mr-xs {
  margin-right: 8rpx;
}
.mr-sm {
  margin-right: 16rpx;
}
.mr-md {
  margin-right: 24rpx;
}
.mr-lg {
  margin-right: 32rpx;
}
.mr-xl {
  margin-right: 48rpx;
}
.p-xs {
  padding: 8rpx;
}
.p-sm {
  padding: 16rpx;
}
.p-md {
  padding: 24rpx;
}
.p-lg {
  padding: 32rpx;
}
.p-xl {
  padding: 48rpx;
}
.pt-xs {
  padding-top: 8rpx;
}
.pt-sm {
  padding-top: 16rpx;
}
.pt-md {
  padding-top: 24rpx;
}
.pt-lg {
  padding-top: 32rpx;
}
.pt-xl {
  padding-top: 48rpx;
}
.pb-xs {
  padding-bottom: 8rpx;
}
.pb-sm {
  padding-bottom: 16rpx;
}
.pb-md {
  padding-bottom: 24rpx;
}
.pb-lg {
  padding-bottom: 32rpx;
}
.pb-xl {
  padding-bottom: 48rpx;
}
.pl-xs {
  padding-left: 8rpx;
}
.pl-sm {
  padding-left: 16rpx;
}
.pl-md {
  padding-left: 24rpx;
}
.pl-lg {
  padding-left: 32rpx;
}
.pl-xl {
  padding-left: 48rpx;
}
.pr-xs {
  padding-right: 8rpx;
}
.pr-sm {
  padding-right: 16rpx;
}
.pr-md {
  padding-right: 24rpx;
}
.pr-lg {
  padding-right: 32rpx;
}
.pr-xl {
  padding-right: 48rpx;
}
.status-pending {
  color: #ff9800;
}
.status-approved {
  color: #4caf50;
}
.status-rejected {
  color: #f44336;
}
.status-expired {
  color: #9e9e9e;
}
.card {
  background-color: #ffffff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  padding: 24rpx;
  margin-bottom: 24rpx;
}
.card-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #212121;
  margin-bottom: 16rpx;
}
.card-content {
  color: #757575;
  line-height: 1.6;
}
.btn-primary {
  background-color: #1976d2;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  transition: 0.2s ease;
}
.btn-primary:active {
  background-color: #1565c0;
}
.btn-secondary {
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  transition: 0.2s ease;
}
.btn-secondary:active {
  background-color: #388e3c;
}
.divider {
  height: 1rpx;
  background-color: #e0e0e0;
  margin: 24rpx 0;
}
/* 每个页面公共css */page{--status-bar-height:25px;--top-window-height:0px;--window-top:0px;--window-bottom:0px;--window-left:0px;--window-right:0px;--window-magin:0px}[data-c-h="true"]{display: none !important;}