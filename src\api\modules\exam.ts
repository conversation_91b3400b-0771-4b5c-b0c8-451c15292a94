/**
 * 考试中心相关API接口
 */
import http from '../../utils/request';
import type { ExamItem, ExamDetail, ExamAnswer, FaceVerifyResult } from '../../types/api';

/**
 * 获取当前考试列表
 */
export function getCurrentExams() {
  return http.get<ExamItem[]>('/exam/current');
}

/**
 * 获取考试详情
 */
export function getExamDetail(examId: string) {
  return http.get<ExamDetail>(`/exam/detail/${examId}`);
}

/**
 * 人脸识别验证
 */
export function verifyFaceIdentity(examId: string, imageData: string) {
  return http.post<FaceVerifyResult>('/exam/verify-face', { examId, imageData });
}

/**
 * 获取考试题目
 */
export function getExamQuestions(examId: string) {
  return http.get<Question[]>(`/exam/${examId}/questions`);
}

/**
 * 提交考试答案
 */
export function submitExamAnswers(examId: string, answers: ExamAnswer[]) {
  return http.post<boolean>(`/exam/${examId}/submit`, { answers });
}

/**
 * 获取历史考试记录
 */
export function getExamHistory(page: number = 1, pageSize: number = 10) {
  return http.get<any>('/exam/history', { page, pageSize });
}
