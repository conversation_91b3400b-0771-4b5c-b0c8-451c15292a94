{"version": 3, "file": "login.js", "sources": ["pages/login/login.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbG9naW4vbG9naW4udnVl"], "sourcesContent": ["<template>\n  <view class=\"login-container\">\n    <view class=\"login-header\">\n      <image class=\"logo\" src=\"/static/logo.png\" mode=\"aspectFit\" />\n      <text class=\"app-name\">疾控医护考试系统</text>\n      <text class=\"app-desc\">专业、权威、便捷的任职资格考试平台</text>\n    </view>\n    \n    <view class=\"login-form\">\n      <view class=\"agreement-section\">\n        <checkbox-group @change=\"onAgreementChange\">\n          <label class=\"agreement-item\">\n            <checkbox :checked=\"agreedToTerms\" />\n            <text class=\"agreement-text\">\n              我已阅读并同意\n              <text class=\"link\" @tap=\"showUserAgreement\">《用户服务协议》</text>\n              和\n              <text class=\"link\" @tap=\"showPrivacyPolicy\">《隐私政策》</text>\n            </text>\n          </label>\n        </checkbox-group>\n      </view>\n      \n      <button \n        class=\"login-btn\" \n        :disabled=\"!agreedToTerms\"\n        @tap=\"handleWxLogin\"\n        open-type=\"getUserInfo\"\n        @getuserinfo=\"onGetUserInfo\"\n      >\n        微信授权登录\n      </button>\n    </view>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref } from 'vue';\nimport { useUserStore } from '../../src/stores/modules/user';\nimport { wxLogin } from '../../src/api/modules/user';\n\nconst userStore = useUserStore();\n\n// 响应式数据\nconst agreedToTerms = ref(false);\n\n/**\n * 协议勾选状态变化\n */\nfunction onAgreementChange(event: any) {\n  agreedToTerms.value = event.detail.value.length > 0;\n}\n\n/**\n * 显示用户协议\n */\nfunction showUserAgreement() {\n  uni.showModal({\n    title: '用户服务协议',\n    content: '这里是用户服务协议的内容...',\n    showCancel: false,\n  });\n}\n\n/**\n * 显示隐私政策\n */\nfunction showPrivacyPolicy() {\n  uni.showModal({\n    title: '隐私政策',\n    content: '这里是隐私政策的内容...',\n    showCancel: false,\n  });\n}\n\n/**\n * 微信登录\n */\nfunction handleWxLogin() {\n  if (!agreedToTerms.value) {\n    uni.showToast({\n      title: '请先同意用户协议',\n      icon: 'none',\n    });\n    return;\n  }\n  \n  uni.login({\n    provider: 'weixin',\n    success: async (loginRes) => {\n      try {\n        const userInfo = await wxLogin({ code: loginRes.code });\n        userStore.setProfile(userInfo);\n        \n        // 根据用户状态跳转\n        if (userInfo.status === 'approved') {\n          uni.switchTab({ url: '/pages/info/info' });\n        } else {\n          uni.navigateTo({ url: '/pages/register/register' });\n        }\n      } catch (error) {\n        uni.__f__('error','at pages/login/login.vue:102','登录失败:', error);\n      }\n    },\n    fail: (error) => {\n      uni.showToast({\n        title: '登录失败，请重试',\n        icon: 'none',\n      });\n      uni.__f__('error','at pages/login/login.vue:110','微信登录失败:', error);\n    },\n  });\n}\n\n/**\n * 获取用户信息回调\n */\nfunction onGetUserInfo(event: any) {\n  uni.__f__('log','at pages/login/login.vue:119','用户信息:', event.detail);\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/src/styles/variables.scss';\n\n.login-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, $primary-color 0%, $primary-light 100%);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: $spacing-xl;\n}\n\n.login-header {\n  text-align: center;\n  margin-bottom: $spacing-xl * 2;\n}\n\n.login-header .logo {\n  width: 120rpx;\n  height: 120rpx;\n  margin-bottom: $spacing-lg;\n}\n\n.login-header .app-name {\n  display: block;\n  font-size: $font-size-xxl;\n  font-weight: $font-weight-bold;\n  color: white;\n  margin-bottom: $spacing-sm;\n}\n\n.login-header .app-desc {\n  display: block;\n  font-size: $font-size-md;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.login-form {\n  width: 100%;\n  max-width: 600rpx;\n}\n\n.agreement-section {\n  margin-bottom: $spacing-xl;\n}\n\n.agreement-section .agreement-item {\n  display: flex;\n  align-items: flex-start;\n}\n\n.agreement-section .agreement-item .agreement-text {\n  flex: 1;\n  font-size: $font-size-sm;\n  color: rgba(255, 255, 255, 0.9);\n  line-height: $line-height-loose;\n  margin-left: $spacing-sm;\n}\n\n.agreement-section .agreement-item .agreement-text .link {\n  color: $accent-color;\n  text-decoration: underline;\n}\n\n.login-btn {\n  width: 100%;\n  height: 88rpx;\n  background-color: white;\n  color: $primary-color;\n  font-size: $font-size-lg;\n  font-weight: $font-weight-medium;\n  border-radius: $border-radius-large;\n  border: none;\n}\n\n.login-btn:disabled {\n  background-color: rgba(255, 255, 255, 0.5);\n  color: rgba(25, 118, 210, 0.5);\n}\n\n.login-btn:not(:disabled):active {\n  background-color: rgba(255, 255, 255, 0.9);\n}\n</style>\n", "import MiniProgramPage from 'E:/project/CDCExamA/pages/login/login.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "ref", "uni", "wxL<PERSON>in"], "mappings": ";;;;;;;;AAyCA,UAAM,YAAYA,wBAAAA;AAGZ,UAAA,gBAAgBC,kBAAI,KAAK;AAK/B,aAAS,kBAAkB,OAAY;AACrC,oBAAc,QAAQ,MAAM,OAAO,MAAM,SAAS;AAAA,IACpD;AAKA,aAAS,oBAAoB;AAC3BC,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MAAA,CACb;AAAA,IACH;AAKA,aAAS,oBAAoB;AAC3BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,MAAA,CACb;AAAA,IACH;AAKA,aAAS,gBAAgB;AACnB,UAAA,CAAC,cAAc,OAAO;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QAAA,CACP;AACD;AAAA,MACF;AAEAA,oBAAAA,MAAI,MAAM;AAAA,QACR,UAAU;AAAA,QACV,SAAS,OAAO,aAAa;AACvB,cAAA;AACF,kBAAM,WAAW,MAAMC,6BAAQ,EAAE,MAAM,SAAS,MAAM;AACtD,sBAAU,WAAW,QAAQ;AAGzB,gBAAA,SAAS,WAAW,YAAY;AAClCD,4BAAAA,MAAI,UAAU,EAAE,KAAK,mBAAoB,CAAA;AAAA,YAAA,OACpC;AACLA,4BAAAA,MAAI,WAAW,EAAE,KAAK,2BAA4B,CAAA;AAAA,YACpD;AAAA,mBACO,OAAO;AACdA,0BAAA,MAAI,MAAM,SAAQ,gCAA+B,SAAS,KAAK;AAAA,UACjE;AAAA,QACF;AAAA,QACA,MAAM,CAAC,UAAU;AACfA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAAA,CACP;AACDA,wBAAA,MAAI,MAAM,SAAQ,gCAA+B,WAAW,KAAK;AAAA,QACnE;AAAA,MAAA,CACD;AAAA,IACH;AAKA,aAAS,cAAc,OAAY;AACjCA,oBAAA,MAAI,MAAM,OAAM,gCAA+B,SAAS,MAAM,MAAM;AAAA,IACtE;;;;;;;;;;;;;;;;ACtHA,GAAG,WAAW,eAAe;"}