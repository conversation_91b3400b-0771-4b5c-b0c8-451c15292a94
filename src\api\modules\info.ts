/**
 * 信息中心相关API接口
 */
import http from '../../utils/request';
import type { InfoItem, InfoListParams } from '../../types/api';

/**
 * 获取公告列表
 */
export function getAnnouncementList(params: InfoListParams) {
  return http.get<InfoItem[]>('/info/announcements', params);
}

/**
 * 获取政策法规列表
 */
export function getPolicyList(params: InfoListParams) {
  return http.get<InfoItem[]>('/info/policies', params);
}

/**
 * 获取重要通知列表
 */
export function getNoticeList(params: InfoListParams) {
  return http.get<InfoItem[]>('/info/notices', params);
}

/**
 * 获取信息详情
 */
export function getInfoDetail(id: string) {
  return http.get<InfoItem>(`/info/detail/${id}`);
}
